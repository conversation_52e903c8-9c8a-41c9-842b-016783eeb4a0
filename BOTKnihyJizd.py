from urllib.parse import unquote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from datetime import datetime, timedelta
import time
import pyautogui
import re

# --- NASTAVENÍ ---
TAB_URL = "https://intranet.img-management.cz/Porady/KNIHY%20JZD/Forms/AllItems.aspx?&&p_SortBehavior=0&p_FileLeafRef=9AL%206014%20%2d%202025%2exls&&PageFirstRow=1&&View={DD08046D-A508-4D0B-BA78-829B558AEBC1}"  # uprav na správnou URL
USERNAME = "januskova"
PASSWORD = "janu943"
EXCEL_PATH = "C:\\Users\\<USER>\\Desktop\\Rozhlasové poplatky - přehled.xlsx"     # uprav na správnou cestu

# --- SELENIUM ---
options = webdriver.ChromeOptions()
options.add_argument('--window-size=1080.800')  # Nastav velikost okna prohlížeče
driver = webdriver.Chrome(options=options)

print("\n--- Přihlašování k webu ---")
print("Spouštím prohlížeč v normálním režimu...")
driver.get(TAB_URL)
print("Čekám na přihlašovací okno...")
time.sleep(1)  # Sníženo z 2 na 1 sekundu
pyautogui.write(USERNAME)
pyautogui.press('tab')
pyautogui.write(PASSWORD)
pyautogui.press('enter')
print("Zadáno uživatelské jméno a heslo do systémového okna.")

wait = WebDriverWait(driver, 15)

print("\n--- Čekám na načtení stránky po přihlášení ---")
try:
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    print("Přihlášení proběhlo, stránka načtena.")
except Exception as e:
    print(f"Chyba při načítání stránky po přihlášení: {e}")



print("\n--- Zpracovávám tabulku souborů (všechny stránky, robustně) ---")

from selenium.common.exceptions import StaleElementReferenceException

def get_range_text():
    try:
        # Najdi rozsah (např. "1 - 30")
        range_elem = driver.find_element(By.XPATH, "//*[contains(text(), '-') and contains(text(), ' ')]")
        return range_elem.text.strip()
    except Exception:
        return None

all_files = []
seen_files = set()
page = 1
while True:
    # Počkej, než se na stránce objeví tabulka (aspoň 1 řádek s odkazem) - rychlejší
    for _ in range(15):  # Sníženo z 30 na 15
        file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
        odkazy = [r for r in file_rows if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
        if len(odkazy) > 0:
            break
        time.sleep(0.1)  # Sníženo z 0.2 na 0.1
    file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
    print(f"Stránka {page}: načteno řádků: {len(file_rows)} (s odkazy: {len(odkazy)})")
    for row in file_rows:
        try:
            link = row.find_element(By.CSS_SELECTOR, "td.ms-vb-title a")
            href = link.get_attribute("href")
            file_name = href.split("/")[-1]
            file_name = unquote(file_name)
            # Najdi datum
            try:
                datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb2 nobr")
                datum_zmeny = datum_cell.text.strip()
            except Exception:
                try:
                    datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb-user.ms-vb-lastcell")
                    datum_zmeny = datum_cell.text.strip()
                except Exception:
                    datum_zmeny = ""
            # Najdi SPZ
            candidates = re.findall(r"[0-9A-Z]{3}[ _\-.]?[0-9A-Z]{3,5}", file_name.upper())
            if candidates:
                spz = candidates[0].strip()
            else:
                candidates2 = re.findall(r"[A-Z0-9]{6,8}", file_name.upper())
                spz = candidates2[0].strip() if candidates2 else ""
            # Unikátnost podle href
            if href and href not in seen_files:
                all_files.append({"spz": spz, "datum_zmeny": datum_zmeny, "file_name": file_name, "href": href})
                seen_files.add(href)
        except Exception:
            continue

    # Najdi tlačítko další (šipka vpravo) podle obrázku next.gif a alt="Další"
    try:
        old_range = get_range_text()
        next_img = driver.find_element(By.CSS_SELECTOR, "a > img[src*='next.gif'][alt='Další']")
        next_btn = next_img.find_element(By.XPATH, "..")  # rodič <a>
        # Pokud je tlačítko neaktivní (disabled), skonči
        if not next_btn.is_enabled():
            print("Tlačítko další je neaktivní, končím stránkování.")
            break
        next_btn.click()
        # Počkej na změnu rozsahu (nebo timeout) a na načtení nové tabulky - rychlejší
        for _ in range(20):  # Sníženo z 40 na 20
            time.sleep(0.15)  # Sníženo z 0.3 na 0.15
            new_range = get_range_text()
            file_rows_new = driver.find_elements(By.CSS_SELECTOR, "tr")
            odkazy_new = [r for r in file_rows_new if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
            if (new_range and new_range != old_range) or (len(odkazy_new) > 0 and len(odkazy_new) != len(odkazy)):
                break
        page += 1
    except Exception:
        print("Tlačítko další nenalezeno, končím stránkování.")
        break

print(f"Celkem načteno unikátních souborů: {len(all_files)}")
print(f"Celkový počet všech řádků (včetně duplicitních): {page * len(file_rows)}")

# Slovník SPZ -> nejnovější datum změny
spz_datum_all = {}  # SPZ -> list dat

# --- AUTOMATICKÉ ROZMEZÍ KONTROL ---
# Rozmezí: 27. aktuálního měsíce až 7. následujícího měsíce
now = datetime.now()

# Začátek: 27. aktuálního měsíce
start_date = now.replace(day=27)
if now.day < 27:
    # Pokud ještě není 27., vezmi 27. předchozího měsíce
    prev_month = now.replace(day=1) - timedelta(days=1)
    start_date = prev_month.replace(day=27)

# Konec: 7. následujícího měsíce
if now.month == 12:
    end_date = now.replace(year=now.year+1, month=1, day=7)
else:
    end_date = now.replace(month=now.month+1, day=7)

print(f"Kontroluji soubory v rozmezí od {start_date.strftime('%d.%m.%Y')} do {end_date.strftime('%d.%m.%Y')}")







# Zpracuj all_files a vytvoř slovník SPZ -> nejnovější datum změny
spz_datum = {}
for file_info in all_files:
    file_name = file_info["file_name"]
    datum_zmeny = file_info["datum_zmeny"]
    spz = file_info["spz"]

    if not spz:  # Přeskoč soubory bez SPZ
        continue

    print(f"DEBUG: Soubor='{file_name}', SPZ='{spz}', datum='{datum_zmeny}'")

    # Převeď datum na datetime pro porovnání (podpora i času)
    file_date = None
    # Podporované formáty s i bez mezer za tečkami
    formats = [
        "%d.%m.%Y %H:%M", "%d.%m.%Y", "%d.%m.%y %H:%M", "%d.%m.%y",
        "%d. %m. %Y %H:%M", "%d. %m. %Y", "%d. %m. %y %H:%M", "%d. %m. %y"
    ]
    for fmt in formats:
        try:
            file_date = datetime.strptime(datum_zmeny, fmt)
            break
        except ValueError:
            pass

    if file_date is None:
        print(f"VAROVÁNÍ: Nepodařilo se převést datum '{datum_zmeny}' u souboru '{file_name}'!")
        continue

    # Pokud SPZ ještě není, nebo je nové datum novější, ulož
    if spz not in spz_datum or file_date > spz_datum[spz][0]:
        spz_datum[spz] = (file_date, datum_zmeny, file_name)

# Výpis výsledků
print("\nSPZ a jejich nejnovější datum změny:")
for spz, (file_date, datum_zmeny, file_name) in spz_datum.items():
    print(f"SPZ: '{spz}' | Datum změny: '{datum_zmeny}' | Soubor: '{file_name}'")



print("\nSPZ z webu:")
for spz in spz_datum.keys():
    print(f"  {spz}")

print("\n--- Ukončuji prohlížeč ---")
driver.quit()

print("\n--- Práce s Excelem ---")
wb = load_workbook(EXCEL_PATH)
print(f"Dostupné listy v Excelu: {wb.sheetnames}")
current_year = str(now.year)
# Najdi list, který obsahuje aktuální rok v názvu
ws_name = None
for name in wb.sheetnames:
    if current_year in name:
        ws_name = name
        break
if ws_name is None:
    print(f"List pro rok {current_year} nenalezen! K dispozici jsou: {wb.sheetnames}")
    exit(1)
else:
    print(f"Používám list: {ws_name}")
ws = wb[ws_name]

header = [cell.value for cell in ws[1]]
print(f"Hlavička tabulky: {header}")
# Najdi sloupec RZ bez ohledu na velikost písmen a mezery
rz_col_idx = None
for idx, col in enumerate(header):
    if col and str(col).strip().lower() == "rz":
        rz_col_idx = idx
        print(f"Sloupec 'RZ' nalezen na pozici {idx+1}.")
        break
if rz_col_idx is None:
    print("Sloupec 'RZ' nenalezen v hlavičce!")
    exit(1)

# Najdi sloupec s názvem obsahujícím 'kniha jízd'
kniha_col_idx = None
for idx, col in enumerate(header):
    if col and 'kniha jízd' in str(col).lower():
        kniha_col_idx = idx
        print(f"Sloupec s knihou jízd nalezen na pozici {idx+1}.")
        break
if kniha_col_idx is None:
    print("Sloupec s knihou jízd nenalezen v hlavičce!")
    exit(1)

red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")


pocet_oznacenych = 0
spz_excel_set = set()
for row in ws.iter_rows(min_row=2):
    spz_excel = str(row[rz_col_idx].value).strip() if row[rz_col_idx].value else ""
    # Přeskoč nevalidní SPZ (musí být 6-8 znaků, jen písmena/čísla a mezery)
    if not re.fullmatch(r"[A-Z0-9 ]{6,8}", spz_excel):
        continue
    spz_excel_set.add(spz_excel)
    cell = row[kniha_col_idx]
    # Normalizace SPZ pro porovnání (ponechá jen písmena a číslice, převede na velká písmena)
    def norm_spz(s):
        return re.sub(r"[^A-Z0-9]", "", s.upper())
    spz_excel_norm = norm_spz(spz_excel)
    # Najdi odpovídající SPZ z webu podle normalizace
    datum = None
    for spz_web, (file_date, datum_zmeny, file_name) in spz_datum.items():
        if norm_spz(spz_web) == spz_excel_norm:
            datum = (file_date, datum_zmeny, file_name)
            break
    # Ladicí výpis pro nenalezené SPZ
    if not datum:
        print(f"DEBUG: Nenalezeno: Excel={repr(spz_excel)} | norm={repr(spz_excel_norm)} | Web SPZ: {[repr(s) for s in spz_datum.keys()]} | Web norm: {[repr(norm_spz(s)) for s in spz_datum.keys()]}")
    if datum and start_date <= datum[0] <= end_date:
        cell.value = True  # zaškrtnuto
        cell.fill = green_fill
        pocet_oznacenych += 1
        print(f"SPZ {spz_excel} má datum v rozmezí {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')} – políčko zaškrtnuto a označeno zeleně.")
    else:
        cell.value = False  # nezaškrtnuto
        cell.fill = red_fill
        if datum:
            print(f"SPZ {spz_excel} má datum {datum[0].strftime('%d.%m.%Y')}, není v rozmezí – políčko nezaškrtnuto a označeno červeně.")
        else:
            print(f"SPZ {spz_excel} nebyla nalezena na webu – políčko nezaškrtnuto a označeno červeně.")

print("\nSPZ z Excelu:")
for spz in sorted(spz_excel_set):
    print(f"  {spz}")

wb.save(EXCEL_PATH)
print(f"Hotovo! Zeleně označené: {pocet_oznacenych}, červeně označené: {ws.max_row-1-pocet_oznacenych}")
