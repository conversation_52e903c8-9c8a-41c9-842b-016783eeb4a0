from urllib.parse import unquote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Pat<PERSON><PERSON>ill
from datetime import datetime, timedelta
import time
import pyautogui
import re
import os

# --- NASTAVENÍ ---
TAB_URL = "https://intranet.img-management.cz/Porady/KNIHY%20JZD/Forms/AllItems.aspx?&&p_SortBehavior=0&p_FileLeafRef=9AL%206014%20%2d%202025%2exls&&PageFirstRow=1&&View={DD08046D-A508-4D0B-BA78-829B558AEBC1}"  # uprav na správnou URL
USERNAME = "januskova"
PASSWORD = "janu943"

# Automatick<PERSON> na<PERSON> plochy (nezávisle na uživateli)
DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")
EXCEL_FILENAME = "Kontrola knihy jízd.xlsx"
EXCEL_PATH = os.path.join(DESKTOP_PATH, EXCEL_FILENAME)

# --- SELENIUM ---
options = webdriver.ChromeOptions()
options.add_argument('--window-size=1080.800')  # Nastav velikost okna prohlížeče
driver = webdriver.Chrome(options=options)

print("\n--- Přihlašování k webu ---")
print("Spouštím prohlížeč v normálním režimu...")
driver.get(TAB_URL)
print("Čekám na přihlašovací okno...")
time.sleep(1)  # Sníženo z 2 na 1 sekundu
pyautogui.write(USERNAME)
pyautogui.press('tab')
pyautogui.write(PASSWORD)
pyautogui.press('enter')
print("Zadáno uživatelské jméno a heslo do systémového okna.")

wait = WebDriverWait(driver, 10)  # Sníženo z 15 na 10 sekund

print("\n--- Čekám na načtení stránky po přihlášení ---")
try:
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    print("Přihlášení proběhlo, stránka načtena.")
except Exception as e:
    print(f"Chyba při načítání stránky po přihlášení: {e}")



print("\n--- Zpracovávám tabulku souborů (všechny stránky, robustně) ---")

from selenium.common.exceptions import StaleElementReferenceException

def get_range_text():
    try:
        # Najdi rozsah (např. "1 - 30")
        range_elem = driver.find_element(By.XPATH, "//*[contains(text(), '-') and contains(text(), ' ')]")
        return range_elem.text.strip()
    except Exception:
        return None

all_files = []
seen_files = set()
page = 1
while True:
    # Počkej, než se na stránce objeví tabulka (aspoň 1 řádek s odkazem) - rychlejší
    for _ in range(15):  # Sníženo z 30 na 15
        file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
        odkazy = [r for r in file_rows if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
        if len(odkazy) > 0:
            break
        time.sleep(0.1)  # Sníženo z 0.2 na 0.1
    file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
    print(f"Stránka {page}: načteno řádků: {len(file_rows)} (s odkazy: {len(odkazy)})")
    for row in file_rows:
        try:
            link = row.find_element(By.CSS_SELECTOR, "td.ms-vb-title a")
            href = link.get_attribute("href")
            file_name = href.split("/")[-1]
            file_name = unquote(file_name)
            # Najdi datum
            try:
                datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb2 nobr")
                datum_zmeny = datum_cell.text.strip()
            except Exception:
                try:
                    datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb-user.ms-vb-lastcell")
                    datum_zmeny = datum_cell.text.strip()
                except Exception:
                    datum_zmeny = ""
            # Najdi SPZ
            candidates = re.findall(r"[0-9A-Z]{3}[ _\-.]?[0-9A-Z]{3,5}", file_name.upper())
            if candidates:
                spz = candidates[0].strip()
            else:
                candidates2 = re.findall(r"[A-Z0-9]{6,8}", file_name.upper())
                spz = candidates2[0].strip() if candidates2 else ""
            # Unikátnost podle href
            if href and href not in seen_files:
                all_files.append({"spz": spz, "datum_zmeny": datum_zmeny, "file_name": file_name, "href": href})
                seen_files.add(href)
        except Exception:
            continue

    # Najdi tlačítko další (šipka vpravo) podle obrázku next.gif a alt="Další"
    try:
        old_range = get_range_text()
        next_img = driver.find_element(By.CSS_SELECTOR, "a > img[src*='next.gif'][alt='Další']")
        next_btn = next_img.find_element(By.XPATH, "..")  # rodič <a>
        # Pokud je tlačítko neaktivní (disabled), skonči
        if not next_btn.is_enabled():
            print("Tlačítko další je neaktivní, končím stránkování.")
            break
        next_btn.click()
        # Počkej na změnu rozsahu (nebo timeout) a na načtení nové tabulky - rychlejší
        for _ in range(20):  # Sníženo z 40 na 20
            time.sleep(0.15)  # Sníženo z 0.3 na 0.15
            new_range = get_range_text()
            file_rows_new = driver.find_elements(By.CSS_SELECTOR, "tr")
            odkazy_new = [r for r in file_rows_new if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
            if (new_range and new_range != old_range) or (len(odkazy_new) > 0 and len(odkazy_new) != len(odkazy)):
                break
        page += 1
    except Exception:
        print("Tlačítko další nenalezeno, končím stránkování.")
        break

print(f"Celkem načteno unikátních souborů: {len(all_files)}")
print(f"Celkový počet všech řádků (včetně duplicitních): {page * len(file_rows)}")

# Slovník SPZ -> nejnovější datum změny
spz_datum_all = {}  # SPZ -> list dat

# --- AUTOMATICKÉ ROZMEZÍ KONTROL ---
# Rozmezí: 27. aktuálního měsíce až 7. následujícího měsíce
now = datetime.now()

# Začátek: 27. aktuálního měsíce
start_date = now.replace(day=27)
if now.day < 27:
    # Pokud ještě není 27., vezmi 27. předchozího měsíce
    prev_month = now.replace(day=1) - timedelta(days=1)
    start_date = prev_month.replace(day=27)

# Konec: 7. následujícího měsíce
if now.month == 12:
    end_date = now.replace(year=now.year+1, month=1, day=7)
else:
    end_date = now.replace(month=now.month+1, day=7)

print(f"Kontroluji soubory v rozmezí od {start_date.strftime('%d.%m.%Y')} do {end_date.strftime('%d.%m.%Y')}")







# Zpracuj all_files a vytvoř slovník SPZ -> nejnovější datum změny
spz_datum = {}
for file_info in all_files:
    file_name = file_info["file_name"]
    datum_zmeny = file_info["datum_zmeny"]
    spz = file_info["spz"]

    if not spz:  # Přeskoč soubory bez SPZ
        continue

    print(f"DEBUG: Soubor='{file_name}', SPZ='{spz}', datum='{datum_zmeny}'")

    # Převeď datum na datetime pro porovnání (podpora i času)
    file_date = None
    # Podporované formáty s i bez mezer za tečkami
    formats = [
        "%d.%m.%Y %H:%M", "%d.%m.%Y", "%d.%m.%y %H:%M", "%d.%m.%y",
        "%d. %m. %Y %H:%M", "%d. %m. %Y", "%d. %m. %y %H:%M", "%d. %m. %y"
    ]
    for fmt in formats:
        try:
            file_date = datetime.strptime(datum_zmeny, fmt)
            break
        except ValueError:
            pass

    if file_date is None:
        print(f"VAROVÁNÍ: Nepodařilo se převést datum '{datum_zmeny}' u souboru '{file_name}'!")
        continue

    # Pokud SPZ ještě není, nebo je nové datum novější, ulož
    if spz not in spz_datum or file_date > spz_datum[spz][0]:
        spz_datum[spz] = (file_date, datum_zmeny, file_name)

# Výpis výsledků
print("\nSPZ a jejich nejnovější datum změny:")
for spz, (file_date, datum_zmeny, file_name) in spz_datum.items():
    print(f"SPZ: '{spz}' | Datum změny: '{datum_zmeny}' | Soubor: '{file_name}'")



print("\nSPZ z webu:")
for spz in spz_datum.keys():
    print(f"  {spz}")

print("\n--- Ukončuji prohlížeč ---")
driver.quit()

print("\n--- Práce s Excelem ---")
print(f"Hledám Excel na ploše: {EXCEL_PATH}")

# Kontrola existence souboru a automatické vytvoření
if not os.path.exists(EXCEL_PATH):
    print("Excel soubor neexistuje, vytvářím nový...")

    # Vytvoř nový Excel soubor
    wb = Workbook()
    ws = wb.active

    # Nastav název listu podle aktuálního roku
    current_year = str(datetime.now().year)
    ws.title = f"Knihy_jizd_{current_year}"

    # Vytvoř hlavičku
    headers = ["Typ", "SPZ", "Jméno", "Datum", "Kniha jízd"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)

    # Ulož nový soubor
    wb.save(EXCEL_PATH)
    print(f"Vytvořen nový Excel soubor: {EXCEL_FILENAME}")

else:
    print("Excel soubor nalezen, načítám...")
    wb = load_workbook(EXCEL_PATH)

print(f"Dostupné listy v Excelu: {wb.sheetnames}")
current_year = str(datetime.now().year)

# Najdi list, který obsahuje aktuální rok v názvu
ws_name = None
for name in wb.sheetnames:
    if current_year in name:
        ws_name = name
        break

# Pokud list pro aktuální rok neexistuje, vytvoř ho
if ws_name is None:
    print(f"List pro rok {current_year} nenalezen, vytvářím nový...")
    ws_name = f"Knihy_jizd_{current_year}"
    ws = wb.create_sheet(ws_name)

    # Vytvoř hlavičku pro nový list
    headers = ["Typ", "SPZ", "Jméno", "Datum", "Kniha jízd"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)

    wb.save(EXCEL_PATH)
    print(f"Vytvořen nový list: {ws_name}")
else:
    print(f"Používám existující list: {ws_name}")
    ws = wb[ws_name]

header = [cell.value for cell in ws[1]]
print(f"Hlavička tabulky: {header}")

# Najdi sloupec se SPZ - zkus různé možné názvy
rz_col_idx = None
possible_names = ["rz", "spz", "registrační značka", "reg. značka", "značka"]

for idx, col in enumerate(header):
    if col:
        col_lower = str(col).strip().lower()
        for name in possible_names:
            if name in col_lower:
                rz_col_idx = idx
                print(f"Sloupec se SPZ '{col}' nalezen na pozici {idx+1}.")
                break
        if rz_col_idx is not None:
            break

# Pokud nenalezen podle názvu, zkus sloupec B (index 1) - typická pozice pro SPZ
if rz_col_idx is None and len(header) > 1:
    print("Sloupec se SPZ nenalezen podle názvu, zkouším sloupec B (pozice 2)...")
    rz_col_idx = 1  # Sloupec B
    print(f"Používám sloupec B (pozice 2) pro SPZ: '{header[1] if len(header) > 1 else 'N/A'}'")

if rz_col_idx is None:
    print("Sloupec se SPZ nenalezen!")
    print(f"Dostupné sloupce: {header}")
    exit(1)

# Najdi sloupec s názvem obsahujícím 'kniha jízd'
kniha_col_idx = None
for idx, col in enumerate(header):
    if col and 'kniha jízd' in str(col).lower():
        kniha_col_idx = idx
        print(f"Sloupec s knihou jízd nalezen na pozici {idx+1}.")
        break
if kniha_col_idx is None:
    print("Sloupec s knihou jízd nenalezen v hlavičce!")
    exit(1)

red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")

# Normalizace SPZ pro porovnání (ponechá jen písmena a číslice, převede na velká písmena)
def norm_spz(s):
    return re.sub(r"[^A-Z0-9]", "", s.upper())

pocet_oznacenych = 0
spz_excel_set = set()

# Funkce pro kontrolu, zda je řádek validní SPZ
def is_valid_spz_row(row_values):
    """Kontroluje, zda řádek obsahuje validní SPZ a není to souhrnný/speciální řádek"""
    if not row_values or len(row_values) <= rz_col_idx:
        return False

    spz = str(row_values[rz_col_idx]).strip() if row_values[rz_col_idx] else ""

    # Přeskoč prázdné SPZ
    if not spz:
        return False

    # Přeskoč souhrnné řádky (obsahují slova jako "Počet", "sám", "platí")
    spz_lower = spz.lower()
    invalid_keywords = ["počet", "sám", "platí", "neplatí", "prodaných", "allitems", "nto", "radio", "kancelář"]
    if any(keyword in spz_lower for keyword in invalid_keywords):
        return False

    # Kontrola formátu SPZ (6-8 znaků, písmena/čísla/mezery)
    if not re.fullmatch(r"[A-Z0-9 ]{6,8}", spz.upper()):
        return False

    # Kontrola, že obsahuje alespoň jedno číslo a jedno písmeno (typické pro SPZ)
    if not (re.search(r"[0-9]", spz) and re.search(r"[A-Z]", spz.upper())):
        return False

    return True

for row in ws.iter_rows(min_row=2):
    row_values = [cell.value for cell in row]

    # Přeskoč nevalidní řádky
    if not is_valid_spz_row(row_values):
        continue

    spz_excel = str(row[rz_col_idx].value).strip()
    spz_excel_set.add(spz_excel)
    cell = row[kniha_col_idx]
    spz_excel_norm = norm_spz(spz_excel)
    # Najdi odpovídající SPZ z webu podle normalizace
    datum = None
    for spz_web, (file_date, datum_zmeny, file_name) in spz_datum.items():
        if norm_spz(spz_web) == spz_excel_norm:
            datum = (file_date, datum_zmeny, file_name)
            break
    # Ladicí výpis pro nenalezené SPZ
    if not datum:
        print(f"DEBUG: Nenalezeno: Excel={repr(spz_excel)} | norm={repr(spz_excel_norm)} | Web SPZ: {[repr(s) for s in spz_datum.keys()]} | Web norm: {[repr(norm_spz(s)) for s in spz_datum.keys()]}")
    if datum and start_date <= datum[0] <= end_date:
        cell.value = True  # zaškrtnuto
        cell.fill = green_fill
        pocet_oznacenych += 1
        print(f"SPZ {spz_excel} má datum v rozmezí {start_date.strftime('%d.%m.%Y')} - {end_date.strftime('%d.%m.%Y')} – políčko zaškrtnuto a označeno zeleně.")
    else:
        cell.value = False  # nezaškrtnuto
        cell.fill = red_fill
        if datum:
            print(f"SPZ {spz_excel} má datum {datum[0].strftime('%d.%m.%Y')}, není v rozmezí – políčko nezaškrtnuto a označeno červeně.")
        else:
            print(f"SPZ {spz_excel} nebyla nalezena na webu – políčko nezaškrtnuto a označeno červeně.")

# --- PŘIDÁNÍ NOVÝCH SPZ Z WEBU ---
print("\n--- Kontroluji SPZ z webu, které nejsou v Excelu ---")

# Vytvoř set normalizovaných SPZ z Excelu
excel_spz_normalized = {norm_spz(spz) for spz in spz_excel_set}

# Najdi SPZ z webu, které nejsou v Excelu
nove_spz = []
for spz_web, (file_date, datum_zmeny, file_name) in spz_datum.items():
    spz_web_norm = norm_spz(spz_web)
    if spz_web_norm not in excel_spz_normalized:
        nove_spz.append((spz_web, file_date, datum_zmeny, file_name))

print(f"Nalezeno {len(nove_spz)} nových SPZ z webu, které nejsou v Excelu:")

# Přidej nové SPZ na konec tabulky
nove_pridane = 0
for spz_web, file_date, datum_zmeny, file_name in nove_spz:
    print(f"  Přidávám: {spz_web} (datum: {datum_zmeny})")

    # Najdi první prázdný řádek
    new_row = ws.max_row + 1

    # Přidej SPZ do sloupce RZ
    ws.cell(row=new_row, column=rz_col_idx + 1, value=spz_web)

    # Zkontroluj, zda splňuje datum rozmezí a nastav knihu jízd
    kniha_cell = ws.cell(row=new_row, column=kniha_col_idx + 1)
    if start_date <= file_date <= end_date:
        kniha_cell.value = True
        kniha_cell.fill = green_fill
        pocet_oznacenych += 1
        print(f"    ✅ SPZ {spz_web} má datum v rozmezí - označeno zeleně")
    else:
        kniha_cell.value = False
        kniha_cell.fill = red_fill
        print(f"    ❌ SPZ {spz_web} má datum {file_date.strftime('%d.%m.%Y')}, není v rozmezí - označeno červeně")

    nove_pridane += 1

print(f"\nPřidáno {nove_pridane} nových SPZ do Excelu")

# --- MAZÁNÍ SPZ, KTERÉ UŽ NEJSOU NA WEBU ---
print("\n--- Kontroluji SPZ z Excelu, které už nejsou na webu ---")

# Vytvoř set normalizovaných SPZ z webu
web_spz_normalized = {norm_spz(spz) for spz in spz_datum.keys()}

# Najdi řádky k smazání (procházej odzadu, aby se neposunuly indexy)
radky_ke_smazani = []
smazane_spz = []

for row_idx in range(ws.max_row, 1, -1):  # Od posledního řádku k druhému
    row = ws[row_idx]
    row_values = [cell.value for cell in row]

    # Přeskoč nevalidní řádky (použij stejnou validaci)
    if not is_valid_spz_row(row_values):
        continue

    spz_excel = str(row[rz_col_idx].value).strip()

    spz_excel_norm = norm_spz(spz_excel)

    # Pokud SPZ není na webu, označ k smazání
    if spz_excel_norm not in web_spz_normalized:
        radky_ke_smazani.append(row_idx)
        smazane_spz.append(spz_excel)
        print(f"  Mažu celý řádek {row_idx}: SPZ {spz_excel} - není na webu")

# Smaž označené celé řádky
for row_idx in radky_ke_smazani:
    ws.delete_rows(row_idx)  # Smaže celý řádek včetně všech sloupců

print(f"Smazáno {len(smazane_spz)} celých řádků s SPZ, které už nejsou na webu")

# --- ŘAZENÍ A FORMÁTOVÁNÍ EXCELU ---
print("\n--- Řadím a formátuji Excel ---")

# Získej všechna data z tabulky (bez hlavičky)
data_rows = []
for row in ws.iter_rows(min_row=2, values_only=True):
    if any(cell is not None for cell in row):  # Přeskoč úplně prázdné řádky
        data_rows.append(list(row))

# Seřaď podle SPZ (sloupec s indexem rz_col_idx)
if data_rows:
    data_rows.sort(key=lambda x: str(x[rz_col_idx] or "").strip().upper())
    print(f"Seřazeno {len(data_rows)} řádků podle SPZ")

    # Smaž všechny řádky s daty (ponech hlavičku)
    if ws.max_row > 1:
        ws.delete_rows(2, ws.max_row - 1)

    # Vlož seřazená data zpět
    for row_data in data_rows:
        ws.append(row_data)

    # Znovu aplikuj formátování (barvy se ztratily při řazení)
    print("Obnovuji formátování...")
    for row_idx, row in enumerate(ws.iter_rows(min_row=2), start=2):
        row_values = [cell.value for cell in row]

        # Přeskoč nevalidní řádky
        if not is_valid_spz_row(row_values):
            continue

        spz_excel = str(row[rz_col_idx].value).strip()

        kniha_cell = row[kniha_col_idx]
        spz_excel_norm = norm_spz(spz_excel)

        # Najdi odpovídající SPZ z webu
        datum = None
        for spz_web, (file_date, datum_zmeny, file_name) in spz_datum.items():
            if norm_spz(spz_web) == spz_excel_norm:
                datum = (file_date, datum_zmeny, file_name)
                break

        # Aplikuj barvy podle data
        if datum and start_date <= datum[0] <= end_date:
            kniha_cell.fill = green_fill
        else:
            kniha_cell.fill = red_fill

# Automatické přizpůsobení šířky sloupců
print("Přizpůsobuji šířku sloupců...")
for column in ws.columns:
    max_length = 0
    column_letter = column[0].column_letter

    for cell in column:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass

    adjusted_width = min(max_length + 2, 50)  # Max 50 znaků
    ws.column_dimensions[column_letter].width = adjusted_width

print("\nSPZ z Excelu (seřazené):")
for spz in sorted(spz_excel_set):
    if spz not in smazane_spz:  # Nezobrazuj smazané SPZ
        print(f"  {spz}")

wb.save(EXCEL_PATH)
print(f"Hotovo! Zeleně označené: {pocet_oznacenych}, červeně označené: {ws.max_row-1-pocet_oznacenych}, nově přidané: {nove_pridane}, smazané: {len(smazane_spz)}")
print("Excel byl seřazen podle SPZ a formátován.")
