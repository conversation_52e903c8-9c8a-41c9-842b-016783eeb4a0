from urllib.parse import unquote
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from openpyxl import load_workbook, Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>ill
from datetime import datetime, timedelta
import time
import pyautogui
import re
import os

# --- NASTAVENÍ ---
TAB_URL = "https://intranet.img-management.cz/Porady/KNIHY%20JZD/Forms/AllItems.aspx?&&p_SortBehavior=0&p_FileLeafRef=9AL%206014%20%2d%202025%2exls&&PageFirstRow=1&&View={DD08046D-A508-4D0B-BA78-829B558AEBC1}"
USERNAME = "januskova"
PASSWORD = "janu943"

# Automatick<PERSON> ploc<PERSON> (nezávisle na uživateli)
DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")
EXCEL_FILENAME = "Kontrola knihy jízd.xlsx"
EXCEL_PATH = os.path.join(DESKTOP_PATH, EXCEL_FILENAME)

# --- SELENIUM ---
options = webdriver.ChromeOptions()
options.add_argument('--window-size=800,600')
driver = webdriver.Chrome(options=options)

print("\n--- Přihlašování k webu ---")
print("Spouštím prohlížeč v normálním režimu...")
driver.get(TAB_URL)
print("Čekám na přihlašovací okno...")
time.sleep(2)
pyautogui.write(USERNAME)
pyautogui.press('tab')
pyautogui.write(PASSWORD)
pyautogui.press('enter')
print("Zadáno uživatelské jméno a heslo do systémového okna.")

wait = WebDriverWait(driver, 15)

print("\n--- Čekám na načtení stránky po přihlášení ---")
try:
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    print("Přihlášení proběhlo, stránka načtena.")
except Exception as e:
    print(f"Chyba při načítání stránky po přihlášení: {e}")

print("\n--- Zpracovávám tabulku souborů (všechny stránky, robustně) ---")

from selenium.common.exceptions import StaleElementReferenceException

def get_range_text():
    try:
        range_elem = driver.find_element(By.XPATH, "//*[contains(text(), '-') and contains(text(), ' ')]")
        return range_elem.text.strip()
    except Exception:
        return None

all_files = []
seen_files = set()
page = 1
while True:
    # Počkej, než se na stránce objeví tabulka (aspoň 1 řádek s odkazem) - rychlejší
    for _ in range(15):
        file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
        odkazy = [r for r in file_rows if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
        if len(odkazy) > 0:
            break
        time.sleep(0.1)
    file_rows = driver.find_elements(By.CSS_SELECTOR, "tr")
    print(f"Stránka {page}: načteno řádků: {len(file_rows)} (s odkazy: {len(odkazy)})")
    for row in file_rows:
        try:
            link = row.find_element(By.CSS_SELECTOR, "td.ms-vb-title a")
            href = link.get_attribute("href")
            file_name = href.split("/")[-1]
            file_name = unquote(file_name)
            # Najdi datum
            try:
                datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb2 nobr")
                datum_zmeny = datum_cell.text.strip()
            except Exception:
                try:
                    datum_cell = row.find_element(By.CSS_SELECTOR, "td.ms-vb-user.ms-vb-lastcell")
                    datum_zmeny = datum_cell.text.strip()
                except Exception:
                    datum_zmeny = ""
            # Najdi SPZ
            candidates = re.findall(r"[0-9A-Z]{3}[ _\-.]?[0-9A-Z]{3,5}", file_name.upper())
            if candidates:
                spz = candidates[0].strip()
            else:
                candidates2 = re.findall(r"[A-Z0-9]{6,8}", file_name.upper())
                spz = candidates2[0].strip() if candidates2 else ""
            # Unikátnost podle href
            if href and href not in seen_files:
                all_files.append({"spz": spz, "datum_zmeny": datum_zmeny, "file_name": file_name, "href": href})
                seen_files.add(href)
        except Exception:
            continue

    # Najdi tlačítko další (šipka vpravo) podle obrázku next.gif a alt="Další"
    try:
        old_range = get_range_text()
        next_img = driver.find_element(By.CSS_SELECTOR, "a > img[src*='next.gif'][alt='Další']")
        next_btn = next_img.find_element(By.XPATH, "..")
        if not next_btn.is_enabled():
            print("Tlačítko další je neaktivní, končím stránkování.")
            break
        next_btn.click()
        # Počkej na změnu rozsahu (nebo timeout) a na načtení nové tabulky - rychlejší
        for _ in range(20):
            time.sleep(0.15)
            new_range = get_range_text()
            file_rows_new = driver.find_elements(By.CSS_SELECTOR, "tr")
            odkazy_new = [r for r in file_rows_new if len(r.find_elements(By.CSS_SELECTOR, "td.ms-vb-title a")) > 0]
            if (new_range and new_range != old_range) or (len(odkazy_new) > 0 and len(odkazy_new) != len(odkazy)):
                break
        page += 1
    except Exception:
        print("Tlačítko další nenalezeno, končím stránkování.")
        break

print(f"Celkem načteno unikátních souborů: {len(all_files)}")

# --- AUTOMATICKÉ ROZMEZÍ KONTROL ---
# Rozmezí: 27. aktuálního měsíce až 7. následujícího měsíce
now = datetime.now()

# Začátek: 27. aktuálního měsíce
start_date = now.replace(day=27)
if now.day < 27:
    # Pokud ještě není 27., vezmi 27. předchozího měsíce
    prev_month = now.replace(day=1) - timedelta(days=1)
    start_date = prev_month.replace(day=27)

# Konec: 7. následujícího měsíce
if now.month == 12:
    end_date = now.replace(year=now.year+1, month=1, day=7)
else:
    end_date = now.replace(month=now.month+1, day=7)

print(f"Kontroluji soubory v rozmezí od {start_date.strftime('%d.%m.%Y')} do {end_date.strftime('%d.%m.%Y')}")

# Zpracuj all_files a vytvoř slovník SPZ -> nejnovější datum změny
spz_datum = {}
for file_info in all_files:
    file_name = file_info["file_name"]
    datum_zmeny = file_info["datum_zmeny"]
    spz = file_info["spz"]
    
    if not spz:
        continue
        
    print(f"DEBUG: Soubor='{file_name}', SPZ='{spz}', datum='{datum_zmeny}'")
    
    # Převeď datum na datetime pro porovnání
    file_date = None
    formats = [
        "%d.%m.%Y %H:%M", "%d.%m.%Y", "%d.%m.%y %H:%M", "%d.%m.%y",
        "%d. %m. %Y %H:%M", "%d. %m. %Y", "%d. %m. %y %H:%M", "%d. %m. %y"
    ]
    for fmt in formats:
        try:
            file_date = datetime.strptime(datum_zmeny, fmt)
            break
        except ValueError:
            pass
    
    if file_date is None:
        print(f"VAROVÁNÍ: Nepodařilo se převést datum '{datum_zmeny}' u souboru '{file_name}'!")
        continue
    
    # Pokud SPZ ještě není, nebo je nové datum novější, ulož
    if spz not in spz_datum or file_date > spz_datum[spz][0]:
        spz_datum[spz] = (file_date, datum_zmeny, file_name)

# Výpis výsledků
print("\nSPZ a jejich nejnovější datum změny:")
for spz, (file_date, datum_zmeny, file_name) in spz_datum.items():
    print(f"SPZ: '{spz}' | Datum změny: '{datum_zmeny}' | Soubor: '{file_name}'")

print("\nSPZ z webu:")
for spz in spz_datum.keys():
    print(f"  {spz}")

print("\n--- Ukončuji prohlížeč ---")
driver.quit()

print("\n--- Práce s Excelem ---")
print(f"Hledám Excel na ploše: {EXCEL_PATH}")

# Kontrola existence souboru a automatické vytvoření
if not os.path.exists(EXCEL_PATH):
    print("Excel soubor neexistuje, vytvářím nový...")
    
    # Vytvoř nový Excel soubor
    wb = Workbook()
    ws = wb.active
    
    # Nastav název listu podle aktuálního roku
    current_year = str(datetime.now().year)
    ws.title = f"Knihy_jizd_{current_year}"
    
    # Vytvoř hlavičku
    headers = ["Typ", "SPZ", "Jméno", "Datum", "Kniha jízd"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # Ulož nový soubor
    wb.save(EXCEL_PATH)
    print(f"Vytvořen nový Excel soubor: {EXCEL_FILENAME}")
    
else:
    print("Excel soubor nalezen, načítám...")
    wb = load_workbook(EXCEL_PATH)

print(f"Dostupné listy v Excelu: {wb.sheetnames}")
current_year = str(datetime.now().year)

# Najdi list, který obsahuje aktuální rok v názvu
ws_name = None
for name in wb.sheetnames:
    if current_year in name:
        ws_name = name
        break

# Pokud list pro aktuální rok neexistuje, vytvoř ho
if ws_name is None:
    print(f"List pro rok {current_year} nenalezen, vytvářím nový...")
    ws_name = f"Knihy_jizd_{current_year}"
    ws = wb.create_sheet(ws_name)
    
    # Vytvoř hlavičku pro nový list
    headers = ["Typ", "SPZ", "Jméno", "Datum", "Kniha jízd"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    wb.save(EXCEL_PATH)
    print(f"Vytvořen nový list: {ws_name}")
else:
    print(f"Používám existující list: {ws_name}")
    ws = wb[ws_name]

header = [cell.value for cell in ws[1]]
print(f"Hlavička tabulky: {header}")

# Najdi sloupec se SPZ - zkus různé možné názvy
rz_col_idx = None
possible_names = ["rz", "spz", "registrační značka", "reg. značka", "značka"]

for idx, col in enumerate(header):
    if col:
        col_lower = str(col).strip().lower()
        for name in possible_names:
            if name in col_lower:
                rz_col_idx = idx
                print(f"Sloupec se SPZ '{col}' nalezen na pozici {idx+1}.")
                break
        if rz_col_idx is not None:
            break

# Pokud nenalezen podle názvu, zkus sloupec B (index 1) - typická pozice pro SPZ
if rz_col_idx is None and len(header) > 1:
    print("Sloupec se SPZ nenalezen podle názvu, zkouším sloupec B (pozice 2)...")
    rz_col_idx = 1  # Sloupec B
    print(f"Používám sloupec B (pozice 2) pro SPZ: '{header[1] if len(header) > 1 else 'N/A'}'")

if rz_col_idx is None:
    print("Sloupec se SPZ nenalezen!")
    print(f"Dostupné sloupce: {header}")
    exit(1)

# Najdi sloupec s názvem obsahujícím 'kniha jízd'
kniha_col_idx = None
for idx, col in enumerate(header):
    if col and 'kniha jízd' in str(col).lower():
        kniha_col_idx = idx
        print(f"Sloupec s knihou jízd nalezen na pozici {idx+1}.")
        break
if kniha_col_idx is None:
    print("Sloupec s knihou jízd nenalezen v hlavičce!")
    exit(1)

red_fill = PatternFill(start_color="FFFF0000", end_color="FFFF0000", fill_type="solid")
green_fill = PatternFill(start_color="FF00FF00", end_color="FF00FF00", fill_type="solid")

# Normalizace SPZ pro porovnání
def norm_spz(s):
    return re.sub(r"[^A-Z0-9]", "", s.upper())

# Funkce pro kontrolu, zda je řádek validní SPZ
def is_valid_spz_row(row_values):
    if not row_values or len(row_values) <= rz_col_idx:
        return False

    spz = str(row_values[rz_col_idx]).strip() if row_values[rz_col_idx] else ""

    if not spz:
        return False

    # Přeskoč souhrnné řádky
    spz_lower = spz.lower()
    invalid_keywords = ["počet", "sám", "platí", "neplatí", "prodaných", "allitems", "nto", "radio", "kancelář"]
    if any(keyword in spz_lower for keyword in invalid_keywords):
        return False

    if not re.fullmatch(r"[A-Z0-9 ]{6,8}", spz.upper()):
        return False

    if not (re.search(r"[0-9]", spz) and re.search(r"[A-Z]", spz.upper())):
        return False

    return True

pocet_oznacenych = 0
spz_excel_set = set()

for row in ws.iter_rows(min_row=2):
    row_values = [cell.value for cell in row]

    if not is_valid_spz_row(row_values):
        continue

    spz_excel = str(row[rz_col_idx].value).strip()
    spz_excel_set.add(spz_excel)
    cell = row[kniha_col_idx]
    spz_excel_norm = norm_spz(spz_excel)

    # Najdi odpovídající SPZ z webu podle normalizace
    datum = None
    for spz_web, (file_date, datum_zmeny, file_name) in spz_datum.items():
        if norm_spz(spz_web) == spz_excel_norm:
            datum = (file_date, datum_zmeny, file_name)
            break

    if datum and start_date <= datum[0] <= end_date:
        cell.value = True
        cell.fill = green_fill
        pocet_oznacenych += 1
        print(f"SPZ {spz_excel} má datum v rozmezí - označeno zeleně.")
    else:
        cell.value = False
        cell.fill = red_fill
        if datum:
            print(f"SPZ {spz_excel} má datum {datum[0].strftime('%d.%m.%Y')}, není v rozmezí - označeno červeně.")
        else:
            print(f"SPZ {spz_excel} nebyla nalezena na webu - označeno červeně.")

wb.save(EXCEL_PATH)
print(f"Hotovo! Zeleně označené: {pocet_oznacenych}, červeně označené: {ws.max_row-1-pocet_oznacenych}")
print(f"Excel uložen: {EXCEL_PATH}")
